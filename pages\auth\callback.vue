<template>
    <div class="min-h-screen bg-trasnparent flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10 text-center">
                <div v-if="status === 'processing'">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <h2 class="text-lg font-medium text-gray-900 mb-2">Sedang memproses log masuk anda...</h2>
                    <p class="text-sm text-gray-600">Sila tunggu sementara kami menyediakan akaun anda.</p>
                </div>

                <div v-else-if="status === 'success'">
                    <div class="rounded-full h-12 w-12 bg-green-100 mx-auto mb-4 flex items-center justify-center">
                        <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <h2 class="text-lg font-medium text-gray-900 mb-2">Log masuk berjaya!</h2>
                    <p class="text-sm text-gray-600">Mengalihkan ke papan pemuka anda...</p>
                </div>

                <div v-else-if="status === 'error'">
                    <div class="rounded-full h-12 w-12 bg-red-100 mx-auto mb-4 flex items-center justify-center">
                        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>
                    <h2 class="text-lg font-medium text-gray-900 mb-2">Log masuk gagal</h2>
                    <p class="text-sm text-gray-600 mb-4">{{ errorMessage }}</p>
                    <button @click="goBackToLogin"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Cuba lagi
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Database } from '~/types/supabase';

type Profile = Database['public']['Tables']['profiles']['Row'];
type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];

const supabase = useSupabaseClient<Database>();
const router = useRouter();

const status = ref<'processing' | 'success' | 'error'>('processing');
const errorMessage = ref('');

onMounted(async () => {
    if (!process.client) return;

    console.log('[simple-callback] OAuth callback started');
    console.log('[simple-callback] Current URL:', window.location.href);

    // Get school code from current domain
    const host = window.location.host;
    const schoolCode = host.split('.')[0];
    console.log('[simple-callback] School code from domain:', schoolCode);

    try {
        // Get session after OAuth
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        console.log('[simple-callback] Session check:', session ? 'SUCCESS' : 'FAILED');

        if (sessionError) {
            console.error('[simple-callback] Session error:', sessionError);
            throw new Error(`Session error: ${sessionError.message}`);
        }

        if (!session || !session.user) {
            console.warn('[simple-callback] No session found, waiting...');

            // Wait a bit and try again
            await new Promise(resolve => setTimeout(resolve, 2000));

            const { data: { session: retrySession }, error: retryError } = await supabase.auth.getSession();

            if (retryError || !retrySession) {
                throw new Error('Authentication failed - no session created');
            }

            await processUser(retrySession, schoolCode);
        } else {
            await processUser(session, schoolCode);
        }

    } catch (error: any) {
        console.error('[simple-callback] Error:', error);
        status.value = 'error';
        errorMessage.value = error.message || 'An unexpected error occurred';
    }
});

const processUser = async (session: any, schoolCode?: string) => {
    try {
        const user = session.user;
        console.log('[simple-callback] Processing user:', user.email);
        console.log('[simple-callback] For school:', schoolCode || 'main domain');

        // Extract name from Google data
        let fullName = '';

        // Try multiple sources for the name
        const sources = [
            user.user_metadata?.full_name,
            user.user_metadata?.name,
            user.identities?.[0]?.identity_data?.full_name,
            user.identities?.[0]?.identity_data?.name,
            user.email?.split('@')[0] // Fallback to email username
        ];

        for (const source of sources) {
            if (source && source.trim()) {
                fullName = source.trim();
                console.log('[simple-callback] Found name from source:', fullName);
                break;
            }
        }

        console.log('[simple-callback] Final name:', fullName);

        // Check if profile exists
        const { data: existingProfile } = await supabase
            .from('profiles')
            .select('id, full_name')
            .eq('id', user.id)
            .single();

        if (existingProfile) {
            console.log('[simple-callback] Profile exists:', existingProfile.full_name);
        } else {
            console.log('[simple-callback] Creating new profile...');

            // Create profile with school code if available
            const profileData: ProfileInsert = {
                id: user.id,
                full_name: fullName || user.email,
                avatar_url: user.user_metadata?.avatar_url || null,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            console.log('[simple-callback] Profile data before insert:', JSON.stringify(profileData, null, 2));

            // Add school code to profile if it's not localhost/main domain
            if (schoolCode && schoolCode !== 'localhost' && schoolCode !== 'www') {
                console.log('[simple-callback] Adding school context:', schoolCode);
                // Note: Add school_code field to profile if needed in future
            }

            console.log('[simple-callback] Final profile data:', JSON.stringify(profileData, null, 2));

            const { data: insertedProfile, error: profileError } = await supabase
                .from('profiles')
                .insert(profileData)
                .select()
                .single();

            if (profileError) {
                console.error('[simple-callback] Profile creation error:', profileError);
                throw new Error(`Failed to create profile: ${profileError.message}`);
            }

            console.log('[simple-callback] Profile created successfully:', insertedProfile);
        }

        // Success!
        status.value = 'success';

        // Redirect to dashboard after brief delay
        setTimeout(() => {
            console.log('[simple-callback] Redirecting to dashboard...');
            router.push('/dashboard');
        }, 1500);

    } catch (error: any) {
        console.error('[simple-callback] Process user error:', error);
        throw error;
    }
};

const goBackToLogin = () => {
    router.push('/auth/simple-login');
};

definePageMeta({
    layout: 'blank',
});

</script>
