browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env.local (tip: ⚙️  write to custom object with { processEnv: myObject }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env (tip: ⚙️  enable debug logging with { debug: true }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr 🏫 [SSR] Detected school subdomain: xba1224 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at D:\XAMPP\htdocs\erphv9\plugins\subdomain-detection.server.ts
  at D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js
  at fn (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js)

subdomain-detection.client.ts:56 🏫 Detected school subdomain: xba1224
a-subdomain.global.ts:288 Fetch finished loading: POST "http://xba1224.localhost:3000/api/schools/exists".
(anonymous) @ index.mjs?v=40128521:21
$fetchRaw2 @ ofetch.03887fc3.mjs?v=40128521:258
$fetch2 @ ofetch.03887fc3.mjs?v=40128521:316
validateSchoolExistsWithCache @ a-subdomain.global.ts:288
(anonymous) @ a-subdomain.global.ts:54
executeAsync @ index.mjs?v=40128521:110
(anonymous) @ a-subdomain.global.ts:54
(anonymous) @ router.js?t=1752970093884&v=40128521:169
fn @ nuxt.js?v=40128521:216
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
callWithNuxt @ nuxt.js?v=40128521:222
(anonymous) @ nuxt.js?v=40128521:38
run @ reactivity.esm-bundler.js?v=40128521:67
runWithContext @ nuxt.js?v=40128521:38
(anonymous) @ router.js?t=1752970093884&v=40128521:169
await in (anonymous)
(anonymous) @ vue-router.mjs?v=40128521:1385
runWithContext @ vue-router.mjs?v=40128521:1360
(anonymous) @ vue-router.mjs?v=40128521:1385
(anonymous) @ vue-router.mjs?v=40128521:1363
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
runWithContext @ vue-router.mjs?v=40128521:2426
(anonymous) @ vue-router.mjs?v=40128521:2698
Promise.then
(anonymous) @ vue-router.mjs?v=40128521:2698
runGuardQueue @ vue-router.mjs?v=40128521:2698
(anonymous) @ vue-router.mjs?v=40128521:2445
Promise.then
navigate @ vue-router.mjs?v=40128521:2439
pushWithRedirect @ vue-router.mjs?v=40128521:2372
push @ vue-router.mjs?v=40128521:2308
replace @ vue-router.mjs?v=40128521:2311
(anonymous) @ router.js?t=1752970093884&v=40128521:223
_function @ index.mjs?v=40128521:133
(anonymous) @ index.mjs?v=40128521:48
(anonymous) @ index.mjs?v=40128521:48
app:created
serialTaskCaller @ index.mjs?v=40128521:46
callHookWith @ index.mjs?v=40128521:198
callHook @ index.mjs?v=40128521:187
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=40128521:7060 <Suspense> is an experimental feature and its API will likely change.
fetch-retry.js?v=40128521:5 Fetch finished loading: POST "https://nhgyywlfopodxomxbegx.supabase.co/auth/v1/token?grant_type=pkce".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ helpers.js?v=40128521:87
_handleRequest @ fetch.js?v=40128521:101
_request @ fetch.js?v=40128521:91
_exchangeCodeForSession @ GoTrueClient.js?v=40128521:417
await in _exchangeCodeForSession
_getSessionFromURL @ GoTrueClient.js?v=40128521:1102
_initialize @ GoTrueClient.js?v=40128521:177
await in _initialize
(anonymous) @ GoTrueClient.js?v=40128521:148
(anonymous) @ GoTrueClient.js?v=40128521:751
(anonymous) @ locks.js?v=40128521:89
confirm.vue:328 [confirm.vue] Component mounted. Processing sign-in...
confirm.vue:329 [confirm.vue] Current URL: http://xba1224.localhost:3000/auth/confirm
confirm.vue:330 [confirm.vue] Current host: xba1224.localhost:3000
confirm.vue:336 [confirm.vue] Is main domain: false
confirm.vue:363 [confirm.vue] Checking session...
confirm.vue:365 [confirm.vue] Session check result: Found session
confirm.vue:367 [confirm.vue] Active session found. Handling successful sign-in.
confirm.vue:134 [confirm.vue] handleSuccessfulSignIn: Session received {access_token: 'eyJhbGciOiJIUzI1NiIsImtpZCI6Ik9VbXRXMzVIWXRqNStUYW…HNlfQ.tJv_WeXN79oHk4JelGbmf4glstrn4GYk8mFgIpwgd2s', token_type: 'bearer', expires_in: 3600, expires_at: 1752974585, refresh_token: 'm5l2ficxbdyy', …}
confirm.vue:141 [confirm.vue] handleSuccessfulSignIn: Set isVerifyingProfile to true.
confirm.vue:169 [confirm.vue] attemptProfileVerification: Attempt 1 for user eb1c652b-b043-410e-a2dc-f0fb15dd96ca
devtools.client.js?v=40128521:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
confirm.vue:295 [confirm.vue] Profile data for user eb1c652b-b043-410e-a2dc-f0fb15dd96ca: {full_name: null}
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
useSchoolMembership.ts:168 [useSchoolMembership] Ensuring membership for user eb1c652b-b043-410e-a2dc-f0fb15dd96ca in school xba1224
useSchoolMembership.ts:94 [useSchoolMembership] Creating membership for user eb1c652b-b043-410e-a2dc-f0fb15dd96ca in school xba1224
useSchoolMembership.ts:108 [useSchoolMembership] Found school: SK Tiong Widu (d3fb479c-1fc4-4ac2-a9f1-246b7c7b800c)
fetch-retry.js?v=40128521:5  GET https://nhgyywlfopodxomxbegx.supabase.co/rest/v1/school_memberships?select=*&user_id=eq.eb1c652b-b043-410e-a2dc-f0fb15dd96ca&school_id=eq.d3fb479c-1fc4-4ac2-a9f1-246b7c7b800c&status=eq.active 406 (Not Acceptable)
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ fetch.js?v=40128521:23
(anonymous) @ fetch.js?v=40128521:44
fulfilled @ fetch.js?v=40128521:4
Promise.then
step @ fetch.js?v=40128521:6
(anonymous) @ fetch.js?v=40128521:7
__awaiter @ fetch.js?v=40128521:3
(anonymous) @ fetch.js?v=40128521:34
then @ @nuxtjs_supabase___@supabase_postgrest-js.js?v=40128521:119
fetch-retry.js?v=40128521:5 Fetch failed loading: GET "https://nhgyywlfopodxomxbegx.supabase.co/rest/v1/school_memberships?select=*&user_id=eq.eb1c652b-b043-410e-a2dc-f0fb15dd96ca&school_id=eq.d3fb479c-1fc4-4ac2-a9f1-246b7c7b800c&status=eq.active".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ fetch.js?v=40128521:23
(anonymous) @ fetch.js?v=40128521:44
fulfilled @ fetch.js?v=40128521:4
Promise.then
step @ fetch.js?v=40128521:6
(anonymous) @ fetch.js?v=40128521:7
__awaiter @ fetch.js?v=40128521:3
(anonymous) @ fetch.js?v=40128521:34
then @ @nuxtjs_supabase___@supabase_postgrest-js.js?v=40128521:119
useSchoolMembership.ts:138 [useSchoolMembership] School membership created successfully: {id: '6c0a0c19-9011-4314-96e7-035352e7c02e', school_id: 'd3fb479c-1fc4-4ac2-a9f1-246b7c7b800c', user_id: 'eb1c652b-b043-410e-a2dc-f0fb15dd96ca', role: 'teacher', status: 'active', …}
fetch-retry.js?v=40128521:5 Fetch finished loading: POST "https://nhgyywlfopodxomxbegx.supabase.co/rest/v1/school_memberships?select=*".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ fetch.js?v=40128521:23
(anonymous) @ fetch.js?v=40128521:44
fulfilled @ fetch.js?v=40128521:4
Promise.then
step @ fetch.js?v=40128521:6
(anonymous) @ fetch.js?v=40128521:7
__awaiter @ fetch.js?v=40128521:3
(anonymous) @ fetch.js?v=40128521:34
then @ @nuxtjs_supabase___@supabase_postgrest-js.js?v=40128521:119
confirm.vue:306 [confirm.vue] School membership ensured successfully: {id: '6c0a0c19-9011-4314-96e7-035352e7c02e', school_id: 'd3fb479c-1fc4-4ac2-a9f1-246b7c7b800c', user_id: 'eb1c652b-b043-410e-a2dc-f0fb15dd96ca', role: 'teacher', status: 'active', …}
useSchoolContext.ts:155 ✅ School context loaded: SK Tiong Widu (xba1224)
