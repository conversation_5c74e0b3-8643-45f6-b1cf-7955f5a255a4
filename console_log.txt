browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env.local (tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env (tip: ⚙️  override existing env vars with { override: true }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr 🏫 [SSR] Detected school subdomain: xba1224 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at D:\XAMPP\htdocs\erphv9\plugins\subdomain-detection.server.ts
  at D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js
  at fn (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js)

subdomain-detection.client.ts:56 🏫 Detected school subdomain: xba1224
fetch-retry.js?v=40128521:5 Fetch finished loading: POST "https://nhgyywlfopodxomxbegx.supabase.co/auth/v1/token?grant_type=pkce".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ helpers.js?v=40128521:87
_handleRequest @ fetch.js?v=40128521:101
_request @ fetch.js?v=40128521:91
_exchangeCodeForSession @ GoTrueClient.js?v=40128521:417
await in _exchangeCodeForSession
_getSessionFromURL @ GoTrueClient.js?v=40128521:1102
_initialize @ GoTrueClient.js?v=40128521:177
await in _initialize
(anonymous) @ GoTrueClient.js?v=40128521:148
(anonymous) @ GoTrueClient.js?v=40128521:751
(anonymous) @ locks.js?v=40128521:89
a-subdomain.global.ts:288 Fetch finished loading: POST "http://xba1224.localhost:3000/api/schools/exists".
(anonymous) @ index.mjs?v=40128521:21
$fetchRaw2 @ ofetch.03887fc3.mjs?v=40128521:258
$fetch2 @ ofetch.03887fc3.mjs?v=40128521:316
validateSchoolExistsWithCache @ a-subdomain.global.ts:288
(anonymous) @ a-subdomain.global.ts:54
executeAsync @ index.mjs?v=40128521:110
(anonymous) @ a-subdomain.global.ts:54
(anonymous) @ router.js?v=40128521:169
fn @ nuxt.js?v=40128521:216
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
callWithNuxt @ nuxt.js?v=40128521:222
(anonymous) @ nuxt.js?v=40128521:38
run @ reactivity.esm-bundler.js?v=40128521:67
runWithContext @ nuxt.js?v=40128521:38
(anonymous) @ router.js?v=40128521:169
await in (anonymous)
(anonymous) @ vue-router.mjs?v=40128521:1385
runWithContext @ vue-router.mjs?v=40128521:1360
(anonymous) @ vue-router.mjs?v=40128521:1385
(anonymous) @ vue-router.mjs?v=40128521:1363
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
runWithContext @ vue-router.mjs?v=40128521:2426
(anonymous) @ vue-router.mjs?v=40128521:2698
Promise.then
(anonymous) @ vue-router.mjs?v=40128521:2698
runGuardQueue @ vue-router.mjs?v=40128521:2698
(anonymous) @ vue-router.mjs?v=40128521:2445
Promise.then
navigate @ vue-router.mjs?v=40128521:2439
pushWithRedirect @ vue-router.mjs?v=40128521:2372
push @ vue-router.mjs?v=40128521:2308
replace @ vue-router.mjs?v=40128521:2311
(anonymous) @ router.js?v=40128521:223
_function @ index.mjs?v=40128521:133
(anonymous) @ index.mjs?v=40128521:48
(anonymous) @ index.mjs?v=40128521:48
app:created
serialTaskCaller @ index.mjs?v=40128521:46
callHookWith @ index.mjs?v=40128521:198
callHook @ index.mjs?v=40128521:187
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=40128521:7060 <Suspense> is an experimental feature and its API will likely change.
devtools.client.js?v=40128521:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
confirm.vue:351 [confirm.vue] Component mounted. Processing sign-in...
confirm.vue:352 [confirm.vue] Current URL: http://xba1224.localhost:3000/auth/confirm?code=547977a2-4805-4da1-83d6-bddf56112e03
confirm.vue:353 [confirm.vue] Current host: xba1224.localhost:3000
confirm.vue:359 [confirm.vue] Is main domain: false
confirm.vue:386 [confirm.vue] Checking session...
confirm.vue:388 [confirm.vue] Session check result: Found session
confirm.vue:390 [confirm.vue] Active session found. Handling successful sign-in.
confirm.vue:134 [confirm.vue] handleSuccessfulSignIn: Session received {access_token: 'eyJhbGciOiJIUzI1NiIsImtpZCI6Ik9VbXRXMzVIWXRqNStUYW…HNlfQ.mAtpZNjOOxxfal5ynIuRDWTex3LsqRBSEctlWI7IyrA', token_type: 'bearer', expires_in: 3600, expires_at: 1752967461, refresh_token: 'qm5qdebyoe2v', …}
confirm.vue:141 [confirm.vue] handleSuccessfulSignIn: Set isVerifyingProfile to true.
confirm.vue:169 [confirm.vue] attemptProfileVerification: Attempt 1 for user c0d04455-a20c-4d4f-871a-80d8ad399e7b
fetch-retry.js?v=40128521:5 Fetch finished loading: GET "https://nhgyywlfopodxomxbegx.supabase.co/rest/v1/profiles?select=full_name&id=eq.c0d04455-a20c-4d4f-871a-80d8ad399e7b".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ fetch.js?v=40128521:23
(anonymous) @ fetch.js?v=40128521:44
fulfilled @ fetch.js?v=40128521:4
Promise.then
step @ fetch.js?v=40128521:6
(anonymous) @ fetch.js?v=40128521:7
__awaiter @ fetch.js?v=40128521:3
(anonymous) @ fetch.js?v=40128521:34
then @ @nuxtjs_supabase___@supabase_postgrest-js.js?v=40128521:119
confirm.vue:337 [confirm.vue] Profile data for user c0d04455-a20c-4d4f-871a-80d8ad399e7b: {full_name: null}
confirm.vue:343 Fetch finished loading: GET "http://xba1224.localhost:3000/api/_nuxt_icon/heroicons.json?icons=academic-cap-solid%2Carrow-path%2Carrow-right-on-rectangle-solid%2Cbars-3-solid%2Cbell-solid%2Ccalendar-days-solid%2Ccalendar-solid%2Cchat-bubble-left-ellipsis-solid%2Cchevron-down-solid%2Cclipboard-document-list-solid%2Ccog-6-tooth-solid%2Cdocument-arrow-up-solid%2Cdocument-chart-bar-solid%2Cdocument-text-solid%2Ceye-solid%2Chome-solid%2Cpaint-brush-solid%2Cpuzzle-piece-solid%2Crectangle-stack-solid%2Cuser-solid%2Cusers-solid".
(anonymous) @ index.mjs?v=40128521:21
$fetch.native @ ofetch.03887fc3.mjs?v=40128521:320
send @ iconify.mjs?v=40128521:742
execNext @ iconify.mjs?v=40128521:1076
setTimeout
sendQuery @ iconify.mjs?v=40128521:1078
query @ iconify.mjs?v=40128521:1093
sendAPIQuery @ iconify.mjs?v=40128521:1171
(anonymous) @ iconify.mjs?v=40128521:1288
(anonymous) @ iconify.mjs?v=40128521:1287
setTimeout
loadNewIcons @ iconify.mjs?v=40128521:1241
(anonymous) @ iconify.mjs?v=40128521:1344
loadIcons @ iconify.mjs?v=40128521:1341
(anonymous) @ iconify.mjs?v=40128521:1356
loadIcon @ iconify.mjs?v=40128521:1350
loadIcon @ shared.js?v=40128521:15
watch.immediate @ css.js?v=40128521:125
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=40128521:204
baseWatchOptions.call @ runtime-core.esm-bundler.js?v=40128521:6244
job @ reactivity.esm-bundler.js?v=40128521:1747
watch @ reactivity.esm-bundler.js?v=40128521:1783
doWatch @ runtime-core.esm-bundler.js?v=40128521:6272
watch @ runtime-core.esm-bundler.js?v=40128521:6205
setup @ css.js?v=40128521:115
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
setupStatefulComponent @ runtime-core.esm-bundler.js?v=40128521:7952
setupComponent @ runtime-core.esm-bundler.js?v=40128521:7913
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5243
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
patchSuspense @ runtime-core.esm-bundler.js?v=40128521:7028
process @ runtime-core.esm-bundler.js?v=40128521:6826
patch @ runtime-core.esm-bundler.js?v=40128521:4750
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5433
run @ reactivity.esm-bundler.js?v=40128521:207
runIfDirty @ reactivity.esm-bundler.js?v=40128521:245
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
flushJobs @ runtime-core.esm-bundler.js?v=40128521:405
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=40128521:319
queueJob @ runtime-core.esm-bundler.js?v=40128521:314
effect2.scheduler @ runtime-core.esm-bundler.js?v=40128521:5475
trigger @ reactivity.esm-bundler.js?v=40128521:235
endBatch @ reactivity.esm-bundler.js?v=40128521:293
notify @ reactivity.esm-bundler.js?v=40128521:566
trigger @ reactivity.esm-bundler.js?v=40128521:540
set value @ reactivity.esm-bundler.js?v=40128521:1412
finalizeNavigation @ vue-router.mjs?v=40128521:2504
(anonymous) @ vue-router.mjs?v=40128521:2414
Promise.then
pushWithRedirect @ vue-router.mjs?v=40128521:2382
(anonymous) @ vue-router.mjs?v=40128521:2400
Promise.then
pushWithRedirect @ vue-router.mjs?v=40128521:2382
push @ vue-router.mjs?v=40128521:2308
replace @ vue-router.mjs?v=40128521:2311
navigateTo @ router.js?v=40128521:140
attemptProfileVerification @ confirm.vue:343
await in attemptProfileVerification
handleSuccessfulSignIn @ confirm.vue:346
(anonymous) @ confirm.vue:391
Promise.then
(anonymous) @ confirm.vue:387
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:2844
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=40128521:204
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js?v=40128521:2824
flushPostFlushCbs @ runtime-core.esm-bundler.js?v=40128521:382
flushJobs @ runtime-core.esm-bundler.js?v=40128521:424
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=40128521:319
queuePostFlushCb @ runtime-core.esm-bundler.js?v=40128521:333
resolve @ runtime-core.esm-bundler.js?v=40128521:7164
resolve @ runtime-core.esm-bundler.js?v=40128521:7171
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7270
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrate @ runtime-core.esm-bundler.js?v=40128521:1673
mount @ runtime-core.esm-bundler.js?v=40128521:3959
app.mount @ runtime-dom.esm-bundler.js?v=40128521:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
fetch-retry.js?v=40128521:5 Fetch finished loading: GET "https://nhgyywlfopodxomxbegx.supabase.co/rest/v1/schools?select=id%2Cname%2Ccode%2Cdescription%2Clocation%2Cestablished%2Csubscription_plan%2Csubscription_status%2Ccreated_at%2Cupdated_at&code=eq.xba1224".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ fetch.js?v=40128521:23
(anonymous) @ fetch.js?v=40128521:44
fulfilled @ fetch.js?v=40128521:4
Promise.then
step @ fetch.js?v=40128521:6
(anonymous) @ fetch.js?v=40128521:7
__awaiter @ fetch.js?v=40128521:3
(anonymous) @ fetch.js?v=40128521:34
then @ @nuxtjs_supabase___@supabase_postgrest-js.js?v=40128521:119
useSchoolContext.ts:155 ✅ School context loaded: SK Tiong Widu (xba1224)
useSchoolContext.ts:139 Fetch finished loading: GET "http://xba1224.localhost:3000/api/_nuxt_icon/heroicons.json?icons=calendar-days%2Cclipboard-document-list%2Cdocument-text%2Clight-bulb%2Cuser".
(anonymous) @ index.mjs?v=40128521:21
$fetch.native @ ofetch.03887fc3.mjs?v=40128521:320
send @ iconify.mjs?v=40128521:742
execNext @ iconify.mjs?v=40128521:1076
setTimeout
sendQuery @ iconify.mjs?v=40128521:1078
query @ iconify.mjs?v=40128521:1093
sendAPIQuery @ iconify.mjs?v=40128521:1171
(anonymous) @ iconify.mjs?v=40128521:1288
(anonymous) @ iconify.mjs?v=40128521:1287
setTimeout
loadNewIcons @ iconify.mjs?v=40128521:1241
(anonymous) @ iconify.mjs?v=40128521:1344
loadIcons @ iconify.mjs?v=40128521:1341
(anonymous) @ iconify.mjs?v=40128521:1356
loadIcon @ iconify.mjs?v=40128521:1350
loadIcon @ shared.js?v=40128521:15
watch.immediate @ css.js?v=40128521:125
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=40128521:204
baseWatchOptions.call @ runtime-core.esm-bundler.js?v=40128521:6244
job @ reactivity.esm-bundler.js?v=40128521:1747
watch @ reactivity.esm-bundler.js?v=40128521:1783
doWatch @ runtime-core.esm-bundler.js?v=40128521:6272
watch @ runtime-core.esm-bundler.js?v=40128521:6205
setup @ css.js?v=40128521:115
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
setupStatefulComponent @ runtime-core.esm-bundler.js?v=40128521:7952
setupComponent @ runtime-core.esm-bundler.js?v=40128521:7913
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5243
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
mountElement @ runtime-core.esm-bundler.js?v=40128521:4880
processElement @ runtime-core.esm-bundler.js?v=40128521:4845
patch @ runtime-core.esm-bundler.js?v=40128521:4713
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
mountSuspense @ runtime-core.esm-bundler.js?v=40128521:6867
process @ runtime-core.esm-bundler.js?v=40128521:6808
patch @ runtime-core.esm-bundler.js?v=40128521:4750
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
processFragment @ runtime-core.esm-bundler.js?v=40128521:5139
patch @ runtime-core.esm-bundler.js?v=40128521:4699
mountChildren @ runtime-core.esm-bundler.js?v=40128521:4957
processFragment @ runtime-core.esm-bundler.js?v=40128521:5139
patch @ runtime-core.esm-bundler.js?v=40128521:4699
patchKeyedChildren @ runtime-core.esm-bundler.js?v=40128521:5756
patchChildren @ runtime-core.esm-bundler.js?v=40128521:5538
processFragment @ runtime-core.esm-bundler.js?v=40128521:5183
patch @ runtime-core.esm-bundler.js?v=40128521:4699
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5433
run @ reactivity.esm-bundler.js?v=40128521:207
runIfDirty @ reactivity.esm-bundler.js?v=40128521:245
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
flushJobs @ runtime-core.esm-bundler.js?v=40128521:405
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=40128521:319
queueJob @ runtime-core.esm-bundler.js?v=40128521:314
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js?v=40128521:6256
effect2.scheduler @ reactivity.esm-bundler.js?v=40128521:1764
trigger @ reactivity.esm-bundler.js?v=40128521:235
endBatch @ reactivity.esm-bundler.js?v=40128521:293
trigger @ reactivity.esm-bundler.js?v=40128521:693
set @ reactivity.esm-bundler.js?v=40128521:972
loadSchoolData @ useSchoolContext.ts:139
await in loadSchoolData
initializeSchoolContext @ useSchoolContext.ts:276
setup @ school.vue:198
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
setupStatefulComponent @ runtime-core.esm-bundler.js?v=40128521:7952
setupComponent @ runtime-core.esm-bundler.js?v=40128521:7913
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5243
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5353
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
processComponent @ runtime-core.esm-bundler.js?v=40128521:5209
patch @ runtime-core.esm-bundler.js?v=40128521:4725
patchSuspense @ runtime-core.esm-bundler.js?v=40128521:7028
process @ runtime-core.esm-bundler.js?v=40128521:6826
patch @ runtime-core.esm-bundler.js?v=40128521:4750
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5433
run @ reactivity.esm-bundler.js?v=40128521:207
runIfDirty @ reactivity.esm-bundler.js?v=40128521:245
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
flushJobs @ runtime-core.esm-bundler.js?v=40128521:405
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=40128521:319
queueJob @ runtime-core.esm-bundler.js?v=40128521:314
effect2.scheduler @ runtime-core.esm-bundler.js?v=40128521:5475
trigger @ reactivity.esm-bundler.js?v=40128521:235
endBatch @ reactivity.esm-bundler.js?v=40128521:293
notify @ reactivity.esm-bundler.js?v=40128521:566
trigger @ reactivity.esm-bundler.js?v=40128521:540
set value @ reactivity.esm-bundler.js?v=40128521:1412
finalizeNavigation @ vue-router.mjs?v=40128521:2504
(anonymous) @ vue-router.mjs?v=40128521:2414
Promise.then
pushWithRedirect @ vue-router.mjs?v=40128521:2382
(anonymous) @ vue-router.mjs?v=40128521:2400
Promise.then
pushWithRedirect @ vue-router.mjs?v=40128521:2382
push @ vue-router.mjs?v=40128521:2308
replace @ vue-router.mjs?v=40128521:2311
navigateTo @ router.js?v=40128521:140
attemptProfileVerification @ confirm.vue:343
await in attemptProfileVerification
handleSuccessfulSignIn @ confirm.vue:346
(anonymous) @ confirm.vue:391
Promise.then
(anonymous) @ confirm.vue:387
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:2844
callWithErrorHandling @ runtime-core.esm-bundler.js?v=40128521:197
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js?v=40128521:204
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js?v=40128521:2824
flushPostFlushCbs @ runtime-core.esm-bundler.js?v=40128521:382
flushJobs @ runtime-core.esm-bundler.js?v=40128521:424
Promise.then
queueFlush @ runtime-core.esm-bundler.js?v=40128521:319
queuePostFlushCb @ runtime-core.esm-bundler.js?v=40128521:333
resolve @ runtime-core.esm-bundler.js?v=40128521:7164
resolve @ runtime-core.esm-bundler.js?v=40128521:7171
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7270
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrate @ runtime-core.esm-bundler.js?v=40128521:1673
mount @ runtime-core.esm-bundler.js?v=40128521:3959
app.mount @ runtime-dom.esm-bundler.js?v=40128521:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
