browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env.local (tip: ⚙️  enable debug logging with { debug: true }) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr [dotenv@17.2.0] injecting env (0) from .env (tip: 🔐 encrypt with dotenvx: https://dotenvx.com) 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at _log (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.configDotenv (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)
  at Object.config (D:\XAMPP\htdocs\erphv9\node_modules\dotenv\lib\main.js)

browser.mjs?v=40128521:48 ssr 🏫 [SSR] Detected school subdomain: xba1224 
  at <anonymous> (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at Object.log (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\core\runtime\nitro\plugins\dev-server-logs.js)
  at D:\XAMPP\htdocs\erphv9\plugins\subdomain-detection.server.ts
  at D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js
  at fn (D:\XAMPP\htdocs\erphv9\node_modules\nuxt\dist\app\nuxt.js)

subdomain-detection.client.ts:56 🏫 Detected school subdomain: xba1224
fetch-retry.js?v=40128521:5 Fetch finished loading: POST "https://nhgyywlfopodxomxbegx.supabase.co/auth/v1/token?grant_type=pkce".
fetchWithRetry @ fetch-retry.js?v=40128521:5
(anonymous) @ helpers.js?v=40128521:87
_handleRequest @ fetch.js?v=40128521:101
_request @ fetch.js?v=40128521:91
_exchangeCodeForSession @ GoTrueClient.js?v=40128521:417
await in _exchangeCodeForSession
_getSessionFromURL @ GoTrueClient.js?v=40128521:1102
_initialize @ GoTrueClient.js?v=40128521:177
await in _initialize
(anonymous) @ GoTrueClient.js?v=40128521:148
(anonymous) @ GoTrueClient.js?v=40128521:751
(anonymous) @ locks.js?v=40128521:89
a-subdomain.global.ts:288 Fetch finished loading: POST "http://xba1224.localhost:3000/api/schools/exists".
(anonymous) @ index.mjs?v=40128521:21
$fetchRaw2 @ ofetch.03887fc3.mjs?v=40128521:258
$fetch2 @ ofetch.03887fc3.mjs?v=40128521:316
validateSchoolExistsWithCache @ a-subdomain.global.ts:288
(anonymous) @ a-subdomain.global.ts:54
executeAsync @ index.mjs?v=40128521:110
(anonymous) @ a-subdomain.global.ts:54
(anonymous) @ router.js?v=40128521:169
fn @ nuxt.js?v=40128521:216
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
callWithNuxt @ nuxt.js?v=40128521:222
(anonymous) @ nuxt.js?v=40128521:38
run @ reactivity.esm-bundler.js?v=40128521:67
runWithContext @ nuxt.js?v=40128521:38
(anonymous) @ router.js?v=40128521:169
await in (anonymous)
(anonymous) @ vue-router.mjs?v=40128521:1385
runWithContext @ vue-router.mjs?v=40128521:1360
(anonymous) @ vue-router.mjs?v=40128521:1385
(anonymous) @ vue-router.mjs?v=40128521:1363
runWithContext @ runtime-core.esm-bundler.js?v=40128521:4016
runWithContext @ vue-router.mjs?v=40128521:2426
(anonymous) @ vue-router.mjs?v=40128521:2698
Promise.then
(anonymous) @ vue-router.mjs?v=40128521:2698
runGuardQueue @ vue-router.mjs?v=40128521:2698
(anonymous) @ vue-router.mjs?v=40128521:2445
Promise.then
navigate @ vue-router.mjs?v=40128521:2439
pushWithRedirect @ vue-router.mjs?v=40128521:2372
push @ vue-router.mjs?v=40128521:2308
replace @ vue-router.mjs?v=40128521:2311
(anonymous) @ router.js?v=40128521:223
_function @ index.mjs?v=40128521:133
(anonymous) @ index.mjs?v=40128521:48
(anonymous) @ index.mjs?v=40128521:48
app:created
serialTaskCaller @ index.mjs?v=40128521:46
callHookWith @ index.mjs?v=40128521:198
callHook @ index.mjs?v=40128521:187
initApp @ entry.js:65
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=40128521:7060 <Suspense> is an experimental feature and its API will likely change.
devtools.client.js?v=40128521:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
runtime-core.esm-bundler.js?v=40128521:50 [Vue warn]: Hydration text content mismatch on <p class=​"text-sm font-medium text-gray-900 dark:​text-white truncate" data-v-021a7e1f>​g-69266534​</p>​ 
  - rendered on server: User
  - expected on client: g-69266534 
  at <Sidebar is-mobile-open=false onClose=fn<closeMobileSidebar> onLogout=fn<handleLogout> > 
  at <Default ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="default" layoutProps= {ref: RefImpl} name="default" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="default" name="default"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=40128521:50
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1921
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=40128521:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5331
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrate @ runtime-core.esm-bundler.js?v=40128521:1673
mount @ runtime-core.esm-bundler.js?v=40128521:3959
app.mount @ runtime-dom.esm-bundler.js?v=40128521:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=40128521:1637 Hydration completed but contains mismatches.
logMismatchError @ runtime-core.esm-bundler.js?v=40128521:1637
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1928
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=40128521:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5331
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrate @ runtime-core.esm-bundler.js?v=40128521:1673
mount @ runtime-core.esm-bundler.js?v=40128521:3959
app.mount @ runtime-dom.esm-bundler.js?v=40128521:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
runtime-core.esm-bundler.js?v=40128521:50 [Vue warn]: Hydration node mismatch:
- rendered on server: <!--[--> (start of fragment) 
- expected on client: div 
  at <Header onToggleMobileSidebar=fn<toggleMobileSidebar> > 
  at <Default ref=Ref< undefined > > 
  at <AsyncComponentWrapper ref=Ref< undefined > > 
  at <LayoutLoader key="default" layoutProps= {ref: RefImpl} name="default" > 
  at <NuxtLayoutProvider layoutProps= {ref: RefImpl} key="default" name="default"  ... > 
  at <NuxtLayout > 
  at <App key=4 > 
  at <NuxtRoot>
warn$1 @ runtime-core.esm-bundler.js?v=40128521:50
handleMismatch @ runtime-core.esm-bundler.js?v=40128521:2068
onMismatch @ runtime-core.esm-bundler.js?v=40128521:1680
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1778
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateChildren @ runtime-core.esm-bundler.js?v=40128521:1998
hydrateElement @ runtime-core.esm-bundler.js?v=40128521:1879
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1780
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
__asyncHydrate @ runtime-core.esm-bundler.js?v=40128521:2469
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5331
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
(anonymous) @ runtime-core.esm-bundler.js?v=40128521:7248
Promise.then
registerDep @ runtime-core.esm-bundler.js?v=40128521:7234
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5250
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrateSuspense @ runtime-core.esm-bundler.js?v=40128521:7311
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1835
hydrateSubTree @ runtime-core.esm-bundler.js?v=40128521:5319
componentUpdateFn @ runtime-core.esm-bundler.js?v=40128521:5337
run @ reactivity.esm-bundler.js?v=40128521:207
setupRenderEffect @ runtime-core.esm-bundler.js?v=40128521:5481
mountComponent @ runtime-core.esm-bundler.js?v=40128521:5256
hydrateNode @ runtime-core.esm-bundler.js?v=40128521:1799
hydrate @ runtime-core.esm-bundler.js?v=40128521:1673
mount @ runtime-core.esm-bundler.js?v=40128521:3959
app.mount @ runtime-dom.esm-bundler.js?v=40128521:1763
initApp @ entry.js:67
await in initApp
(anonymous) @ entry.js:75
simple-callback.vue:56 [simple-callback] OAuth callback started
simple-callback.vue:57 [simple-callback] Current URL: http://xba1224.localhost:3000/auth/simple-callback?code=0144a1d5-4f2a-45d9-9a2a-e0b309cf031a
simple-callback.vue:62 [simple-callback] School code from domain: xba1224
simple-callback.vue:68 [simple-callback] Session check: SUCCESS
simple-callback.vue:102 [simple-callback] Processing user: <EMAIL>
simple-callback.vue:103 [simple-callback] For school: xba1224
simple-callback.vue:120 [simple-callback] Found name from source: COURTNEY V. SUNGGIP KPM-Guru
simple-callback.vue:125 [simple-callback] Final name: COURTNEY V. SUNGGIP KPM-Guru
simple-callback.vue:135 [simple-callback] Profile exists: null
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
simple-callback.vue:170 [simple-callback] Redirecting to dashboard...
useSchoolContext.ts:155 ✅ School context loaded: SK Tiong Widu (xba1224)
