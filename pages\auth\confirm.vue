<template>
  <div>
    <div class="flex flex-col items-center justify-center min-h-screen bg-transparent p-4">
      <div class="bg-white p-8 rounded-lg shadow-md text-center max-w-md w-full">
        <Icon name="svg-spinners:180-ring-with-bg" size="48" class="mb-6 text-primary mx-auto" />
        <h1 class="text-2xl font-semibold mb-4 text-gray-800">Sedang Memproses</h1>
        <p class="text-gray-600 mb-2">
          Sila tunggu sebentar sementara kami memeriksa status akaun anda...
        </p>
        <p v-if="statusMessage" class="text-sm text-gray-500 italic">{{ statusMessage }}</p>
        <p v-if="countdown > 0 && (errorRedirecting || profileNotFoundRedirecting)" class="text-sm text-gray-500 mt-2">
          Melanjutkan dalam {{ countdown }} saat...
        </p>
        <div v-if="showRetryButton" class="mt-6">
          <Button @click="manualRetry" variant="secondary" size="sm">
            Retry Check
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted as onCleanup, nextTick } from 'vue';
import { useSupabaseClient, navigateTo } from '#imports';
import type { Session, User, PostgrestError } from '@supabase/supabase-js';
import type { Database } from '~/types/supabase';
import Button from '~/components/ui/base/Button.vue';
import Icon from '~/components/ui/base/Icon.vue';

definePageMeta({
  layout: 'blank',
});

interface UserProfile {
  is_profile_complete: boolean;
  full_name: string | null;
}

const supabase = useSupabaseClient<Database>();

const MAX_RETRIES = 5;
const RETRY_DELAY_PROFILE_NOT_FOUND = 2000;
const RETRY_DELAY_EXCEPTION = 3000;
const QUERY_TIMEOUT_MS = 10000; // Increased to 10 seconds

const processingSignIn = ref(true);
const statusMessage = ref<string | null>('Initializing...');
const isVerifyingProfile = ref(false); // Guard flag

const errorRedirectTimeoutId = ref<NodeJS.Timeout | null>(null);
const profileCheckRetryTimeoutId = ref<NodeJS.Timeout | null>(null);
const profileCheckAttempts = ref(0); // Added for retry logic

const countdown = ref(0);
const countdownIntervalId = ref<NodeJS.Timeout | null>(null);
const errorRedirecting = ref(false);
const profileNotFoundRedirecting = ref(false);
const showRetryButton = ref(false);

const startCountdownAndRedirect = async (path: string, seconds: number) => {
  countdown.value = seconds;
  if (countdownIntervalId.value) clearInterval(countdownIntervalId.value);
  countdownIntervalId.value = setInterval(async () => {
    countdown.value -= 1;
    if (countdown.value <= 0) {
      if (countdownIntervalId.value) clearInterval(countdownIntervalId.value);
      processingSignIn.value = false;
      isVerifyingProfile.value = false; // Reset guard on navigation
      await nextTick();
      await navigateTo(path, { replace: true });
    }
  }, 1000);
};

// Simplified profile check - just verify profile exists
const fetchProfileDataOnce = async (userId: string): Promise<{
  profile?: UserProfile | null;
  error?: PostgrestError | Error | null;
  timedOut?: boolean;
}> => {
  // console.log(`[confirm.vue] fetchProfileDataOnce: Querying profile for user ${userId}.`);
  statusMessage.value = `Memproses profail pengguna ${userId}...`;

  const profileQuery = supabase
    .from('profiles')
    .select('full_name')
    .eq('id', userId)
    .maybeSingle<UserProfile>();

  const queryTimeoutPromise = new Promise((_, rejectTimeout) =>
    setTimeout(() => rejectTimeout(new Error('Profile query timed out after ' + QUERY_TIMEOUT_MS + 'ms')), QUERY_TIMEOUT_MS)
  );

  try {
    // @ts-ignore
    const result = await Promise.race([profileQuery, queryTimeoutPromise]) as { data: UserProfile | null; error: PostgrestError | null; status: number; };
    // console.log('[confirm.vue] Supabase profile query result:', result);
    return { profile: result.data, error: result.error };
  } catch (e) {
    // console.warn(`[confirm.vue] Profile query timed out or other race error for user ${userId}.`, e);
    return { error: e as Error, timedOut: true };
  }
};

const manualRetry = async () => {
  // console.log('[confirm.vue] Manual retry initiated.');
  showRetryButton.value = false;
  errorRedirecting.value = false;
  profileNotFoundRedirecting.value = false;
  if (countdownIntervalId.value) clearInterval(countdownIntervalId.value);
  countdown.value = 0;
  statusMessage.value = 'Mencuba semula...';
  processingSignIn.value = true;

  supabase.auth.getSession().then(async ({ data: { session } }) => {
    if (session && session.user) {
      await handleSuccessfulSignIn(session);
    } else {
      statusMessage.value = 'Tiada sesi aktif. Mengubah hala ke halaman log masuk.';
      errorRedirecting.value = true;
      await startCountdownAndRedirect('/auth/login', 5); // Updated from /auth/google-auth
    }
  }).catch(async err => {
    statusMessage.value = 'Ralat untuk mendapatkan sesi gagal. Mengubah hala ke halaman log masuk.';
    errorRedirecting.value = true;
    await startCountdownAndRedirect('/auth/login', 5); // Updated from /auth/google-auth
  });
};

// Rewritten handleSuccessfulSignIn
const handleSuccessfulSignIn = async (session: Session | null) => {
  console.log('[confirm.vue] handleSuccessfulSignIn: Session received', session);

  if (isVerifyingProfile.value) {
    console.log('[confirm.vue] Profile verification already in progress. Skipping duplicate call.');
    return;
  }
  isVerifyingProfile.value = true;
  console.log('[confirm.vue] handleSuccessfulSignIn: Set isVerifyingProfile to true.');

  clearAllTimers();
  errorRedirecting.value = false;
  profileNotFoundRedirecting.value = false;
  showRetryButton.value = false;
  countdown.value = 0;
  profileCheckAttempts.value = 0; // Reset attempts for this sign-in flow

  if (!session?.user?.id) {
    console.error('[confirm.vue] No user ID in session.');
    statusMessage.value = 'Ralat: Tiada ID pengguna dalam sesi. Mengubah hala ke halaman log masuk.';
    errorRedirecting.value = true;
    await startCountdownAndRedirect('/auth/login', 5);
    isVerifyingProfile.value = false; // Ensure reset before exiting
    return;
  }

  const userId = session.user.id;
  statusMessage.value = 'Authentication successful. Verifying profile...';

  const attemptProfileVerification = async () => {
    if (!isVerifyingProfile.value && profileCheckAttempts.value > 0) {
      console.log('[confirm.vue] attemptProfileVerification: isVerifyingProfile became false or component unmounted. Aborting further retries.');
      return;
    }

    profileCheckAttempts.value++;
    console.log(`[confirm.vue] attemptProfileVerification: Attempt ${profileCheckAttempts.value} for user ${userId}`);
    statusMessage.value = `Menyemak status profil (Percubaan ${profileCheckAttempts.value})...`;

    const { profile, error, timedOut } = await fetchProfileDataOnce(userId);

    if (!isVerifyingProfile.value) { // Check again in case of async gap and unmount/cancellation
      console.log('[confirm.vue] attemptProfileVerification: Verification cancelled during fetch. Aborting.');
      return;
    }

    if (error) {
      console.error(`[confirm.vue] Error fetching profile (Attempt ${profileCheckAttempts.value}):`, error);
      if (profileCheckAttempts.value < MAX_RETRIES) {
        statusMessage.value = timedOut ? `Proses tamat tempoh (Percubaan ${profileCheckAttempts.value}). Mencuba semula...` : `Ralat mendapatkan profil: ${(error as Error).message} (Percubaan ${profileCheckAttempts.value}). Mencuba semula...`;
        profileCheckRetryTimeoutId.value = setTimeout(attemptProfileVerification, timedOut ? RETRY_DELAY_PROFILE_NOT_FOUND : RETRY_DELAY_EXCEPTION);
      } else {
        statusMessage.value = 'Gagal mengesahkan profil selepas beberapa percubaan. Sila cuba log masuk semula.';
        errorRedirecting.value = true;
        showRetryButton.value = false; // Or true, depending on desired UX
        await startCountdownAndRedirect('/auth/login', 10);
        // isVerifyingProfile is reset by startCountdownAndRedirect
      }
      return;
    }

    if (!profile) {
      console.warn(`[confirm.vue] Profile not found for user ${userId}. Attempt ${profileCheckAttempts.value}.`);

      // For Google OAuth users, try to create profile automatically
      if (profileCheckAttempts.value === 1) {
        statusMessage.value = 'Creating user profile...';

        try {
          // Get current session to access user metadata
          const { data: { session } } = await supabase.auth.getSession()

          if (session?.user) {
            const user = session.user

            // Create profile for teacher (not school admin)
            // Extract full name from Google metadata with comprehensive debugging
            console.log('[confirm.vue] User metadata:', user.user_metadata)
            console.log('[confirm.vue] User identities:', user.identities)
            console.log('[confirm.vue] User app_metadata:', user.app_metadata)

            // Try multiple sources for the full name - Google provides name in different fields
            const fullName = user.user_metadata?.full_name ||
              user.user_metadata?.name ||
              user.user_metadata?.display_name ||
              user.identities?.[0]?.identity_data?.full_name ||
              user.identities?.[0]?.identity_data?.name ||
              user.identities?.[0]?.identity_data?.display_name ||
              user.app_metadata?.full_name ||
              user.app_metadata?.name ||
              `${user.user_metadata?.given_name || ''} ${user.user_metadata?.family_name || ''}`.trim() ||
              `${user.identities?.[0]?.identity_data?.given_name || ''} ${user.identities?.[0]?.identity_data?.family_name || ''}`.trim() ||
              user.email?.split('@')[0] || // Fallback to email username
              'Teacher';

            console.log('[confirm.vue] Extracted full name:', fullName)

            const profileData: Database['public']['Tables']['profiles']['Insert'] = {
              id: user.id,
              full_name: fullName,
              avatar_url: user.user_metadata?.avatar_url || user.identities?.[0]?.identity_data?.avatar_url || null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };

            console.log('[confirm.vue] Creating profile with data:', profileData);

            const { data: newProfile, error: createError } = await supabase
              .from('profiles')
              .insert(profileData)
              .select()
              .single()

            if (createError) {
              console.error('[confirm.vue] Error creating profile:', createError)
              // Continue with retry logic below
            } else {
              console.log('[confirm.vue] Profile created successfully:', newProfile);
              // Profile created successfully, now create school membership
              statusMessage.value = 'Creating school membership...';

              try {
                // Get current school code from subdomain or localStorage (for OAuth redirects)
                const currentHost = window.location.host;
                let schoolCode = currentHost.split('.')[0];

                // If we're on the main domain, check localStorage for OAuth school code
                if (currentHost === 'localhost:3000') {
                  const storedSchoolCode = localStorage.getItem('oauth_school_code');
                  if (storedSchoolCode) {
                    schoolCode = storedSchoolCode;
                    console.log('[confirm.vue] Using stored school code from OAuth:', schoolCode);
                  } else {
                    console.error('[confirm.vue] No school code found - unable to create membership');
                    // Continue anyway - profile exists
                  }
                }

                if (schoolCode && schoolCode !== 'localhost') {
                  console.log('[confirm.vue] Creating membership for school:', schoolCode);

                  // Get school ID first
                  const { data: school, error: schoolError } = await (supabase as any)
                    .from('schools')
                    .select('id')
                    .eq('code', schoolCode.toLowerCase())
                    .single()

                  if (schoolError || !school) {
                    console.error('[confirm.vue] Error finding school:', schoolError)
                  } else {
                    // Create school membership for teacher
                    const { error: membershipError } = await (supabase as any)
                      .from('school_memberships')
                      .insert({
                        user_id: user.id,
                        school_id: school.id,
                        role: 'teacher',
                        status: 'active',
                        joined_at: new Date().toISOString(),
                        permissions: {}
                      })

                    if (membershipError) {
                      console.error('[confirm.vue] Error creating school membership:', membershipError)
                      // Continue anyway - profile exists
                    } else {
                      console.log('[confirm.vue] School membership created successfully')
                    }
                  }
                } else {
                  console.warn('[confirm.vue] No valid school code found for membership creation')
                }
              } catch (membershipErr) {
                console.error('[confirm.vue] Error in membership creation:', membershipErr)
                // Continue anyway - profile exists
              }

              // Profile created successfully, redirect via home route for proper middleware handling
              statusMessage.value = 'Setup complete. Redirecting...';
              isVerifyingProfile.value = false;
              await nextTick();
              await navigateTo('/', { replace: true });
              return;
            }
          }
        } catch (error) {
          console.error('[confirm.vue] Error creating profile:', error)
          // Continue with retry logic below
        }
      }

      if (profileCheckAttempts.value < MAX_RETRIES) {
        statusMessage.value = `Profile data not yet available. Retrying (Attempt ${profileCheckAttempts.value})...`;
        profileCheckRetryTimeoutId.value = setTimeout(attemptProfileVerification, RETRY_DELAY_PROFILE_NOT_FOUND);
      } else {
        statusMessage.value = 'Profile setup may be needed or verification timed out. Redirecting...';
        profileNotFoundRedirecting.value = true;
        await startCountdownAndRedirect('/auth/login', 5);
        // isVerifyingProfile is reset by startCountdownAndRedirect
      }
      return;
    }

    console.log(`[confirm.vue] Profile data for user ${userId}:`, profile);
    clearAllTimers(); // Clear any pending retry timers
    // Profile exists - redirect via home route for proper middleware handling
    statusMessage.value = 'Profail lengkap. Mengubah hala...';
    isVerifyingProfile.value = false;
    await nextTick();
    await navigateTo('/', { replace: true });
  };

  await attemptProfileVerification(); // Start the first attempt
  // isVerifyingProfile is managed by attemptProfileVerification or startCountdownAndRedirect
};

onMounted(() => {
  console.log('[confirm.vue] Component mounted. Processing sign-in...');
  console.log('[confirm.vue] Current URL:', window.location.href);
  console.log('[confirm.vue] Current host:', window.location.host);

  // Check if we're on the main domain and need to redirect to school subdomain
  const currentHost = window.location.host;
  const isMainDomain = currentHost === 'localhost:3000';

  console.log('[confirm.vue] Is main domain:', isMainDomain);

  if (isMainDomain) {
    // Check if we have a stored school code from OAuth flow
    const storedSchoolCode = localStorage.getItem('oauth_school_code');
    console.log('[confirm.vue] Stored school code:', storedSchoolCode);

    if (storedSchoolCode) {
      // Redirect to school subdomain with the current URL parameters
      const schoolSubdomain = `${storedSchoolCode}.localhost:3000`;
      const currentUrl = new URL(window.location.href);
      const redirectUrl = `http://${schoolSubdomain}${currentUrl.pathname}${currentUrl.search}${currentUrl.hash}`;

      console.log('[confirm.vue] Redirecting to school subdomain:', redirectUrl);

      // Clear the stored school code
      localStorage.removeItem('oauth_school_code');

      // Redirect to school subdomain
      window.location.href = redirectUrl;
      return;
    } else {
      console.warn('[confirm.vue] No stored school code found for OAuth redirect');
    }
  }

  // Immediately check the session on mount
  console.log('[confirm.vue] Checking session...');
  supabase.auth.getSession().then(async ({ data: { session } }) => {
    console.log('[confirm.vue] Session check result:', session ? 'Found session' : 'No session');
    if (session && session.user) {
      console.log('[confirm.vue] Active session found. Handling successful sign-in.');
      await handleSuccessfulSignIn(session);
    } else {
      console.log('[confirm.vue] No active session. Redirecting to login.');
      errorRedirecting.value = true;
      await startCountdownAndRedirect('/auth/login', 5);
    }
  }).catch(async err => {
    console.error('[confirm.vue] Error fetching session:', err);
    statusMessage.value = 'Error fetching session. Redirecting to sign-in.';
    errorRedirecting.value = true;
    await startCountdownAndRedirect('/auth/login', 5);
  });
});

onCleanup(() => {
  // console.log('[confirm.vue] Component unmounted. Cleaning up timers...');
  clearAllTimers();
});

const clearAllTimers = () => {
  if (countdownIntervalId.value) clearInterval(countdownIntervalId.value);
  if (errorRedirectTimeoutId.value) clearTimeout(errorRedirectTimeoutId.value);
  if (profileCheckRetryTimeoutId.value) clearTimeout(profileCheckRetryTimeoutId.value);
  countdownIntervalId.value = null;
  errorRedirectTimeoutId.value = null;
  profileCheckRetryTimeoutId.value = null;
  // console.log('[confirm.vue] Cleared all timers.');
};
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
